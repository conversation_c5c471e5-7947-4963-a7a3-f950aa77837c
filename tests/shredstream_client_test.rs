use std::time::Duration;

use shreder::common::client::{
    config::{ShredstreamClientConfig, ShredstreamClientFilters},
    shredstream_client::ShredstreamClient,
};

#[tokio::test]
async fn test_client_creation() {
    let config = ShredstreamClientConfig {
        connect_timeout: Duration::from_secs(5),
        subscribe_timeout: Duration::from_secs(10),
        filters: ShredstreamClientFilters {
            accounts: Some(vec!["test_account".to_string()]),
        },
    };
    
    let client = ShredstreamClient::new("https://test.endpoint".to_string(), config);
    
    client.reset().await;
}

#[tokio::test]
async fn test_client_subscribe_with_invalid_endpoint() {
    let config = ShredstreamClientConfig {
        connect_timeout: Duration::from_millis(100),
        subscribe_timeout: Duration::from_millis(100),
        filters: ShredstreamClientFilters {
            accounts: Some(vec!["test_account".to_string()]),
        },
    };
    
    let client = ShredstreamClient::new("https://invalid.endpoint".to_string(), config);
    
    let result = client.subscribe().await;
    assert!(result.is_err());
    
    client.reset().await;
}
